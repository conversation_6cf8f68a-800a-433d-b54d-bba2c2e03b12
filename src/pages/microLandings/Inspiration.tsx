import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import { useReferral } from "@/hooks/use-referral.tsx";
import { useTranslation } from "react-i18next";

const features = [
    {
        title: 'Подбор визуальных референсов по описанию',
        description: 'Просто опишите атмосферу или ключевые детали — ИИ создаст подборку уникальных картинок, идеально передающих нужное настроение.',
        image: 'inspiration/1.webp',
        reverse: false
    },
    {
        title: 'Генерация цветовых палитр',
        description: 'На основе вашей идеи ИИ предложит гармоничные цветовые сочетания с примерами их применения в дизайне и фото.',
        image: 'inspiration/2.webp',
        reverse: true
    },
    {
        title: 'Поиск стиля через смешение направлений',
        description: 'Опишите два или больше стиля (например, «ретро» и «минимализм») — ИИ создаст уникальные гибридные визуалы, которые могут стать новым фирменным стилем.',
        image: 'inspiration/3.webp',
        reverse: false
    },
    {
        title: 'Визуализация концепций до фотосессии',
        description: 'ИИ поможет увидеть, как будет выглядеть проект или продукт, ещё до съёмок или дизайна, чтобы избежать ошибок и быстрее согласовать идею.',
        image: 'inspiration/4.webp',
        reverse: true
    },
    {
        title: 'Случайные креативные генерации',
        description: 'Когда нет конкретной идеи, ИИ может выдавать неожиданные, но визуально цепляющие комбинации, которые становятся отправной точкой для нового проекта.',
        image: 'inspiration/5.webp',
        reverse: false
    }
]

const LandingInspiration = () => {
    const _ = useReferral();
    const { t } = useTranslation();
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title={t("landings.inspiration.title")} titleHighlight={t("landings.inspiration.titleHighlight")} subtitle={t("landings.inspiration.subtitle")} secondaryImage='/heroImages/inspiration/landingHero2.png' topSecondaryImage='/heroImages/inspiration/landingHero3.png' bottomSecondaryImage='/heroImages/inspiration/landingHero4.png'/>
            <Features features={features}/>
            <FAQ />
            <Footer />  
        </div>
    );
};

export default LandingInspiration;
